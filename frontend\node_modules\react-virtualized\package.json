{"name": "react-virtualized", "description": "React components for efficiently rendering large, scrollable lists and tabular data", "author": "<PERSON> <<EMAIL>>", "user": "b<PERSON><PERSON>n", "version": "9.22.6", "homepage": "https://github.com/bvaughn/react-virtualized", "main": "dist/commonjs/index.js", "module": "dist/es/index.js", "jsnext:main": "dist/es/index.js", "license": "MIT", "scripts": {"build:types": "flow-copy-source --ignore \"**/*.{jest,e2e,ssr,example}.js\" source/WindowScroller dist/es/WindowScroller && flow-copy-source --ignore \"**/*.{jest,e2e,ssr,example}.js\" source/AutoSizer dist/es/AutoSizer", "build": "yarn run build:commonjs && yarn run build:css && yarn run build:es && yarn run build:demo && yarn run build:umd", "build:commonjs": "yarn run clean:commonjs && cross-env NODE_ENV=commonjs babel source --out-dir dist/commonjs", "build:css": "postcss source/styles.css -o styles.css --use autoprefixer", "build:demo": "yarn run clean:demo && cross-env NODE_ENV=production webpack --config webpack.config.demo.js -p --bail", "build:es": "yarn run clean:es && yarn run build:types && cross-env NODE_ENV=es babel source --out-dir dist/es", "build:umd": "yarn run clean:umd && cross-env NODE_ENV=rollup rollup -c", "clean": "yarn run clean:commonjs && yarn run clean:demo && yarn run clean:es && yarn run clean:umd", "clean:commonjs": "rimraf dist/commonjs", "clean:demo": "<PERSON><PERSON><PERSON> build", "clean:es": "rimraf dist/es", "clean:umd": "rimraf dist/umd", "deploy": "gh-pages -d build", "lint": "eslint 'source/**/*.js'", "typecheck": "flow check", "prettier": "prettier --write '{playground,source,docs}/**/*.{js,md}'", "prettier:diff": "prettier --list-different '{playground,source,docs}/**/*.{js,md}'", "postpublish": "yarn run deploy", "prepublishOnly": "yarn run build", "start": "cross-env NODE_ENV=development webpack-dev-server --hot --config webpack.config.dev.js", "test": "yarn run test:jest", "test:jest": "jest --no-watchman --runInBand", "test:coverage": "jest --no-watchman --maxWorkers 2 --coverage && codecov", "watch": "watch 'clear && yarn run test -s' source", "watch:jest": "jest --no-watchman --watch"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "files": ["dist", "styles.css"], "repository": {"type": "git", "url": "https://github.com/bvaughn/react-virtualized.git"}, "keywords": ["react", "reactjs", "react-component", "virtual", "list", "scrolling", "infinite", "virtualized", "table", "fixed", "header", "flex", "flexbox", "grid", "spreadsheet"], "bugs": {"url": "https://github.com/bvaughn/react-virtualized/issues"}, "devDependencies": {"@babel/cli": "^7.7.0", "@babel/core": "^7.7.2", "@babel/plugin-external-helpers": "^7.2.0", "@babel/plugin-proposal-class-properties": "^7.7.0", "@babel/plugin-transform-flow-comments": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "^7.7.0", "@babel/plugin-transform-runtime": "^7.6.2", "@babel/polyfill": "^7.7.0", "@babel/preset-env": "^7.7.1", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.7.0", "@babel/preset-stage-2": "^7.0.0", "autoprefixer": "^9.7.1", "babel-eslint": "^10.0.3", "babel-jest": "^24.9.0", "babel-loader": "8.0.6", "babel-plugin-flow-react-proptypes": "^26.0.0", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "codecov": "^3.6.1", "codemirror": "^5.49.2", "cross-env": "^6.0.3", "css-loader": "^3.2.0", "eslint": "^6.6.0", "eslint-config-fbjs": "^3.1.1", "eslint-config-prettier": "^6.5.0", "eslint-config-react": "^1.1.7", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-flowtype": "^4.3.0", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-react": "^7.16.0", "eslint-plugin-relay": "^1.3.12", "extract-text-webpack-plugin": "^3.0.2", "file-loader": "^4.2.0", "flow-bin": "^0.111.3", "flow-copy-source": "^2.0.8", "gh-pages": "^2.1.1", "html-webpack-plugin": "^3.2.0", "husky": "^3.0.9", "immutable": "^4.0.0-rc.12", "jest": "^24.9.0", "jest-environment-puppeteer": "^4.3.0", "lint-staged": "^9.4.2", "postcss": "^7.0.21", "postcss-cli": "^6.1.3", "postcss-loader": "^3.0.0", "prettier": "1.19.1", "pretty-quick": "^2.0.1", "puppeteer": "^2.0.0", "react": "^17.0.1", "react-codemirror": "^1.0.0", "react-dom": "^17.0.1", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-test-renderer": "^17.0.1", "rimraf": "^6.0.1", "rollup": "^1.26.5", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-uglify": "^6.0.3", "style-loader": "^1.0.0", "watch": "^1.0.2", "webpack": "^4.41.2", "webpack-cli": "^3.3.10", "webpack-dev-server": "^3.9.0"}, "dependencies": {"@babel/runtime": "^7.7.2", "clsx": "^1.0.4", "dom-helpers": "^5.1.3", "loose-envify": "^1.4.0", "prop-types": "^15.7.2", "react-lifecycles-compat": "^3.0.4"}, "peerDependencies": {"react": "^16.3.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.3.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "browserify": {"transform": ["loose-envify"]}}